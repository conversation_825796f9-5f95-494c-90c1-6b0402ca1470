{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "npx sequelize-cli db:migrate && node index.js", "dev": "nodemon index.js", "db:migrate": "npx sequelize-cli db:migrate", "db:migrate:undo": "npx sequelize-cli db:migrate:undo", "db:migrate:undo:all": "npx sequelize-cli db:migrate:undo:all", "db:seed": "npx sequelize-cli db:seed:all", "db:seed:undo": "npx sequelize-cli db:seed:undo:all"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-list-endpoints": "^7.1.1", "fs": "^0.0.1-security", "http-status-codes": "^2.3.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "path": "^0.12.7", "sequelize": "^6.37.5", "sharp": "^0.33.5"}, "devDependencies": {"nodemon": "^3.1.9"}}