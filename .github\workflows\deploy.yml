name: Deploy Backend

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Deploy to VPS
      uses: appleboy/ssh-action@v1.0.0
      with:
        host: ${{ secrets.VPS_HOST }}
        username: ${{ secrets.VPS_USER }}
        key: ${{ secrets.DEPLOY_KEY }}
        script: |
          cd /home/<USER>/deployment/obituary-app/obituary-fe
          sleep 3
          npm install
          /root/.nvm/versions/node/v21.7.3/bin/pm2 restart obituary-be
