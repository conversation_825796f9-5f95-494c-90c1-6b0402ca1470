{"timestamp": "2025-08-08T18:33:37.641Z", "totalLogs": 41, "totalErrors": 8, "success": false, "logs": [{"timestamp": "2025-08-08T18:33:35.620Z", "message": "Starting complete migration process...", "type": "info"}, {"timestamp": "2025-08-08T18:33:35.620Z", "message": "Starting migration step: <PERSON><PERSON>a Creation", "type": "info"}, {"timestamp": "2025-08-08T18:33:35.620Z", "message": "Creating Supabase schema...", "type": "info"}, {"timestamp": "2025-08-08T18:33:36.533Z", "message": "Schema created successfully", "type": "info"}, {"timestamp": "2025-08-08T18:33:36.762Z", "message": "Schema created successfully", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.393Z", "message": "Schema created successfully", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.626Z", "message": "Schema created successfully", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.626Z", "message": "Completed migration step: Schema Creation", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.626Z", "message": "Starting migration step: Users", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.627Z", "message": "Migrating users...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.633Z", "message": "Failed to migrate users: Table 'obituary-db.users' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.633Z", "message": "Completed migration step: Users", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.633Z", "message": "Starting migration step: <PERSON><PERSON><PERSON>", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.633Z", "message": "Migrating refresh tokens...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.634Z", "message": "Failed to migrate refresh tokens: Table 'obituary-db.refreshTokens' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.634Z", "message": "Completed migration step: <PERSON><PERSON><PERSON>", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.634Z", "message": "Starting migration step: Cemetries", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.635Z", "message": "Migrating cemetries...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.635Z", "message": "Failed to migrate cemetries: Table 'obituary-db.cemetries' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.635Z", "message": "Completed migration step: Cemetries", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.635Z", "message": "Starting migration step: Obituaries", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.636Z", "message": "Migrating obituaries...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.637Z", "message": "Failed to migrate obituaries: Table 'obituary-db.obituaries' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.637Z", "message": "Completed migration step: Obituaries", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.637Z", "message": "Starting migration step: Candles", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.637Z", "message": "Migrating candles...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.638Z", "message": "Failed to migrate candles: Table 'obituary-db.candles' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.638Z", "message": "Completed migration step: Candles", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.638Z", "message": "Starting migration step: Visi<PERSON>", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.638Z", "message": "Migrating visits...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.639Z", "message": "Failed to migrate visits: Table 'obituary-db.visits' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.639Z", "message": "Completed migration step: Visits", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.639Z", "message": "Starting migration step: Photos", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.639Z", "message": "Migrating photos...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.639Z", "message": "Failed to migrate photos: Table 'obituary-db.photos' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.640Z", "message": "Completed migration step: Photos", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.640Z", "message": "Starting migration step: Condolences", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.640Z", "message": "Migrating condolences...", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.640Z", "message": "Failed to migrate condolences: Table 'obituary-db.condolences' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:33:37.640Z", "message": "Completed migration step: Condolences", "type": "info"}, {"timestamp": "2025-08-08T18:33:37.640Z", "message": "Generating migration report...", "type": "info"}], "errors": [{"message": "Table 'obituary-db.users' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM users ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.users' doesn't exist"}, {"message": "Table 'obituary-db.refreshTokens' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM refreshTokens ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.refreshTokens' doesn't exist"}, {"message": "Table 'obituary-db.cemetries' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM cemetries ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.cemetries' doesn't exist"}, {"message": "Table 'obituary-db.obituaries' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM obituaries ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.obituaries' doesn't exist"}, {"message": "Table 'obituary-db.candles' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM candles ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.candles' doesn't exist"}, {"message": "Table 'obituary-db.visits' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM visits ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.visits' doesn't exist"}, {"message": "Table 'obituary-db.photos' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM photos ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.photos' doesn't exist"}, {"message": "Table 'obituary-db.condolences' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM condolences ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.condolences' doesn't exist"}]}