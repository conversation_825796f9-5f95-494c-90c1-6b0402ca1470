{"timestamp": "2025-08-08T18:49:15.617Z", "totalLogs": 34, "totalErrors": 8, "success": false, "idMappings": 0, "logs": [{"timestamp": "2025-08-08T18:49:15.608Z", "message": "Starting complete migration process...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.608Z", "message": "Starting migration step: Users", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.609Z", "message": "Migrating users to profiles...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.611Z", "message": "Failed to migrate users: Table 'obituary-db.users' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.611Z", "message": "Completed migration step: Users", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.611Z", "message": "Starting migration step: <PERSON><PERSON><PERSON>", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.611Z", "message": "Migrating refresh tokens...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.612Z", "message": "Failed to migrate refresh tokens: Table 'obituary-db.refreshTokens' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.612Z", "message": "Completed migration step: <PERSON><PERSON><PERSON>", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.612Z", "message": "Starting migration step: Cemetries", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.612Z", "message": "Migrating cemetries...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.613Z", "message": "Failed to migrate cemetries: Table 'obituary-db.cemetries' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.613Z", "message": "Completed migration step: Cemetries", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.613Z", "message": "Starting migration step: Obituaries", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.613Z", "message": "Migrating obituaries...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.614Z", "message": "Failed to migrate obituaries: Table 'obituary-db.obituaries' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.614Z", "message": "Completed migration step: Obituaries", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.614Z", "message": "Starting migration step: Candles", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.614Z", "message": "Migrating candles...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.614Z", "message": "Failed to migrate candles: Table 'obituary-db.candles' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.614Z", "message": "Completed migration step: Candles", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.615Z", "message": "Starting migration step: Visi<PERSON>", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.615Z", "message": "Migrating visits...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.615Z", "message": "Failed to migrate visits: Table 'obituary-db.visits' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.615Z", "message": "Completed migration step: Visits", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.615Z", "message": "Starting migration step: Photos", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.615Z", "message": "Migrating photos...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.616Z", "message": "Failed to migrate photos: Table 'obituary-db.photos' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.616Z", "message": "Completed migration step: Photos", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.616Z", "message": "Starting migration step: Condolences", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.616Z", "message": "Migrating condolences...", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.617Z", "message": "Failed to migrate condolences: Table 'obituary-db.condolences' doesn't exist", "type": "error"}, {"timestamp": "2025-08-08T18:49:15.617Z", "message": "Completed migration step: Condolences", "type": "info"}, {"timestamp": "2025-08-08T18:49:15.617Z", "message": "Generating migration report...", "type": "info"}], "errors": [{"message": "Table 'obituary-db.users' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM users ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.users' doesn't exist"}, {"message": "Table 'obituary-db.refreshTokens' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM refreshTokens ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.refreshTokens' doesn't exist"}, {"message": "Table 'obituary-db.cemetries' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM cemetries ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.cemetries' doesn't exist"}, {"message": "Table 'obituary-db.obituaries' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM obituaries ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.obituaries' doesn't exist"}, {"message": "Table 'obituary-db.candles' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM candles ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.candles' doesn't exist"}, {"message": "Table 'obituary-db.visits' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM visits ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.visits' doesn't exist"}, {"message": "Table 'obituary-db.photos' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM photos ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.photos' doesn't exist"}, {"message": "Table 'obituary-db.condolences' doesn't exist", "code": "ER_NO_SUCH_TABLE", "errno": 1146, "sql": "SELECT * FROM condolences ORDER BY id", "sqlState": "42S02", "sqlMessage": "Table 'obituary-db.condolences' doesn't exist"}]}